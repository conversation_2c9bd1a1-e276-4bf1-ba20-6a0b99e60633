{"name": "flahasoil-api", "version": "1.0.0", "description": "FlahaSoil Backend API - Flaha Agri Tech PA Division", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "docker:build": "docker build -t flahasoil-api .", "docker:run": "docker run -p 3001:3001 flahasoil-api", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "node scripts/seed.js", "db:studio": "prisma studio", "db:reset": "prisma migrate reset"}, "keywords": ["agriculture", "soil-analysis", "precision-agriculture", "flaha-agritech"], "author": "Flaha Agri Tech PA Division", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "node-fetch": "^2.7.0", "nodemailer": "^6.9.7", "prisma": "^5.7.1", "validator": "^13.15.0", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}